<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的商品 - 汾酒拍卖商家端</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'fenjiu-red': '#C41E3A',
                        'fenjiu-gold': '#D4AF37',
                        'fenjiu-dark': '#2C3E50'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="bg-black text-white text-xs px-4 py-1 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="bg-white border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="text-gray-600">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-lg font-bold text-fenjiu-dark">我的商品</h1>
            <button onclick="navigateTo('add-product.html')" class="text-fenjiu-red">
                <i class="fas fa-plus text-xl"></i>
            </button>
        </div>
    </div>

    <!-- 筛选栏 -->
    <div class="bg-white px-4 py-3 border-b border-gray-200">
        <div class="flex items-center space-x-4">
            <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option>全部状态</option>
                <option>在售中</option>
                <option>已下架</option>
                <option>已售完</option>
            </select>
            <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option>全部分类</option>
                <option>白酒</option>
                <option>收藏版</option>
                <option>限量版</option>
            </select>
            <button class="px-4 py-2 bg-fenjiu-red text-white rounded-lg text-sm">
                <i class="fas fa-search mr-1"></i>筛选
            </button>
        </div>
    </div>

    <!-- 商品列表 -->
    <div class="px-4 py-4 pb-20">
        <!-- 商品卡片 1 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="flex">
                <div class="w-24 h-24 bg-gray-200 flex-shrink-0">
                    <img src="https://via.placeholder.com/96x96/C41E3A/FFFFFF?text=汾酒" 
                         alt="汾酒青花瓷" class="w-full h-full object-cover">
                </div>
                <div class="flex-1 p-4">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-medium text-gray-900 text-sm">汾酒青花瓷系列 500ml</h3>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">在售</span>
                    </div>
                    <div class="text-fenjiu-red font-bold text-lg mb-2">¥1,280</div>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>库存：15件</span>
                        <span>销量：8件</span>
                        <span>浏览：156次</span>
                    </div>
                </div>
            </div>
            <div class="px-4 py-3 bg-gray-50 flex items-center justify-between">
                <div class="flex space-x-3">
                    <button class="text-blue-600 text-sm">
                        <i class="fas fa-edit mr-1"></i>编辑
                    </button>
                    <button class="text-fenjiu-red text-sm">
                        <i class="fas fa-exchange-alt mr-1"></i>转拍卖
                    </button>
                    <button class="text-gray-600 text-sm">
                        <i class="fas fa-eye-slash mr-1"></i>下架
                    </button>
                </div>
                <button class="text-gray-400">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- 商品卡片 2 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="flex">
                <div class="w-24 h-24 bg-gray-200 flex-shrink-0">
                    <img src="https://via.placeholder.com/96x96/D4AF37/FFFFFF?text=汾酒" 
                         alt="汾酒珍藏版" class="w-full h-full object-cover">
                </div>
                <div class="flex-1 p-4">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-medium text-gray-900 text-sm">汾酒珍藏版 1988年</h3>
                        <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">已售完</span>
                    </div>
                    <div class="text-fenjiu-red font-bold text-lg mb-2">¥5,800</div>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>库存：0件</span>
                        <span>销量：3件</span>
                        <span>浏览：89次</span>
                    </div>
                </div>
            </div>
            <div class="px-4 py-3 bg-gray-50 flex items-center justify-between">
                <div class="flex space-x-3">
                    <button class="text-blue-600 text-sm">
                        <i class="fas fa-plus mr-1"></i>补货
                    </button>
                    <button class="text-green-600 text-sm">
                        <i class="fas fa-chart-line mr-1"></i>数据
                    </button>
                    <button class="text-gray-600 text-sm">
                        <i class="fas fa-copy mr-1"></i>复制
                    </button>
                </div>
                <button class="text-gray-400">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- 商品卡片 3 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="flex">
                <div class="w-24 h-24 bg-gray-200 flex-shrink-0">
                    <img src="https://via.placeholder.com/96x96/2C3E50/FFFFFF?text=汾酒" 
                         alt="汾酒限量版" class="w-full h-full object-cover">
                </div>
                <div class="flex-1 p-4">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-medium text-gray-900 text-sm">汾酒限量版 龙年纪念</h3>
                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">已下架</span>
                    </div>
                    <div class="text-fenjiu-red font-bold text-lg mb-2">¥2,680</div>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>库存：5件</span>
                        <span>销量：12件</span>
                        <span>浏览：234次</span>
                    </div>
                </div>
            </div>
            <div class="px-4 py-3 bg-gray-50 flex items-center justify-between">
                <div class="flex space-x-3">
                    <button class="text-green-600 text-sm">
                        <i class="fas fa-eye mr-1"></i>上架
                    </button>
                    <button class="text-blue-600 text-sm">
                        <i class="fas fa-edit mr-1"></i>编辑
                    </button>
                    <button class="text-fenjiu-red text-sm">
                        <i class="fas fa-exchange-alt mr-1"></i>转拍卖
                    </button>
                </div>
                <button class="text-gray-400">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- 商品卡片 4 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="flex">
                <div class="w-24 h-24 bg-gray-200 flex-shrink-0">
                    <img src="https://via.placeholder.com/96x96/8B4513/FFFFFF?text=汾酒" 
                         alt="汾酒陈年老酒" class="w-full h-full object-cover">
                </div>
                <div class="flex-1 p-4">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-medium text-gray-900 text-sm">汾酒陈年老酒 20年</h3>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">在售</span>
                    </div>
                    <div class="text-fenjiu-red font-bold text-lg mb-2">¥3,200</div>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>库存：8件</span>
                        <span>销量：5件</span>
                        <span>浏览：67次</span>
                    </div>
                </div>
            </div>
            <div class="px-4 py-3 bg-gray-50 flex items-center justify-between">
                <div class="flex space-x-3">
                    <button class="text-blue-600 text-sm">
                        <i class="fas fa-edit mr-1"></i>编辑
                    </button>
                    <button class="text-fenjiu-red text-sm">
                        <i class="fas fa-exchange-alt mr-1"></i>转拍卖
                    </button>
                    <button class="text-gray-600 text-sm">
                        <i class="fas fa-eye-slash mr-1"></i>下架
                    </button>
                </div>
                <button class="text-gray-400">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- 加载更多 -->
        <div class="text-center py-4">
            <button class="text-gray-500 text-sm">
                <i class="fas fa-spinner mr-2"></i>加载更多商品
            </button>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="grid grid-cols-4 py-2">
            <button onclick="navigateTo('dashboard.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 text-fenjiu-red">
                <i class="fas fa-box text-xl mb-1"></i>
                <span class="text-xs">藏品</span>
            </button>
            <button onclick="navigateTo('orders.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-clipboard-list text-xl mb-1"></i>
                <span class="text-xs">订单</span>
            </button>
            <button onclick="navigateTo('analytics.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </div>

    <script>
        function navigateTo(page) {
            window.location.href = page;
        }
    </script>
</body>
</html>
