<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 汾酒拍卖商家端</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'fenjiu-red': '#C41E3A',
                        'fenjiu-gold': '#D4AF37',
                        'fenjiu-dark': '#2C3E50'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="bg-black text-white text-xs px-4 py-1 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="bg-fenjiu-red text-white px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-wine-bottle text-xl mr-3"></i>
                <div>
                    <h1 class="font-bold text-lg">汾酒拍卖</h1>
                    <p class="text-red-100 text-xs">商家端</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <button class="relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-fenjiu-gold text-xs w-4 h-4 rounded-full flex items-center justify-center">3</span>
                </button>
                <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-fenjiu-red"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="px-4 py-6 pb-20">
        <!-- 数据概览 -->
        <div class="mb-6">
            <h2 class="text-lg font-bold text-fenjiu-dark mb-4">
                <i class="fas fa-chart-line text-fenjiu-red mr-2"></i>数据概览
            </h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">在售商品</span>
                        <i class="fas fa-box text-blue-500"></i>
                    </div>
                    <div class="text-2xl font-bold text-fenjiu-dark">24</div>
                    <div class="text-xs text-green-500">
                        <i class="fas fa-arrow-up mr-1"></i>+3 本周
                    </div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">拍卖中</span>
                        <i class="fas fa-gavel text-fenjiu-red"></i>
                    </div>
                    <div class="text-2xl font-bold text-fenjiu-dark">8</div>
                    <div class="text-xs text-green-500">
                        <i class="fas fa-arrow-up mr-1"></i>+2 本周
                    </div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">本月收入</span>
                        <i class="fas fa-yen-sign text-fenjiu-gold"></i>
                    </div>
                    <div class="text-2xl font-bold text-fenjiu-dark">¥12.8万</div>
                    <div class="text-xs text-green-500">
                        <i class="fas fa-arrow-up mr-1"></i>+15.2%
                    </div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-600 text-sm">待处理</span>
                        <i class="fas fa-clock text-orange-500"></i>
                    </div>
                    <div class="text-2xl font-bold text-fenjiu-dark">5</div>
                    <div class="text-xs text-orange-500">
                        <i class="fas fa-exclamation-triangle mr-1"></i>需关注
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="mb-6">
            <h2 class="text-lg font-bold text-fenjiu-dark mb-4">
                <i class="fas fa-bolt text-fenjiu-red mr-2"></i>快捷操作
            </h2>
            <div class="grid grid-cols-4 gap-4">
                <button onclick="navigateTo('add-product.html')" class="bg-white rounded-lg p-4 shadow-sm text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-plus-circle text-2xl text-blue-500 mb-2"></i>
                    <div class="text-xs text-gray-700">发布商品</div>
                </button>
                <button onclick="navigateTo('add-auction.html')" class="bg-white rounded-lg p-4 shadow-sm text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-upload text-2xl text-fenjiu-red mb-2"></i>
                    <div class="text-xs text-gray-700">上传拍品</div>
                </button>
                <button onclick="navigateTo('orders.html')" class="bg-white rounded-lg p-4 shadow-sm text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-clipboard-list text-2xl text-green-500 mb-2"></i>
                    <div class="text-xs text-gray-700">订单管理</div>
                </button>
                <button onclick="navigateTo('analytics.html')" class="bg-white rounded-lg p-4 shadow-sm text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-chart-bar text-2xl text-purple-500 mb-2"></i>
                    <div class="text-xs text-gray-700">数据分析</div>
                </button>
            </div>
        </div>

        <!-- 待处理事项 -->
        <div class="mb-6">
            <h2 class="text-lg font-bold text-fenjiu-dark mb-4">
                <i class="fas fa-tasks text-fenjiu-red mr-2"></i>待处理事项
            </h2>
            <div class="bg-white rounded-lg shadow-sm">
                <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-orange-500 mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">待付款订单</div>
                            <div class="text-sm text-gray-500">3个订单等待买家付款</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-blue-500 mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">拍卖即将结束</div>
                            <div class="text-sm text-gray-500">2个拍品将在今日结束</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="p-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-star text-fenjiu-gold mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">新的评价</div>
                            <div class="text-sm text-gray-500">收到5条新的商品评价</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 最新动态 -->
        <div>
            <h2 class="text-lg font-bold text-fenjiu-dark mb-4">
                <i class="fas fa-newspaper text-fenjiu-red mr-2"></i>最新动态
            </h2>
            <div class="space-y-3">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                        <div class="flex-1">
                            <div class="text-sm text-gray-900">您的拍品"1988年汾酒珍藏版"有新的出价</div>
                            <div class="text-xs text-gray-500 mt-1">当前价格：¥8,800 · 2分钟前</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                        <div class="flex-1">
                            <div class="text-sm text-gray-900">商品"汾酒青花瓷系列"已成功售出</div>
                            <div class="text-xs text-gray-500 mt-1">成交价格：¥1,280 · 1小时前</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-fenjiu-red rounded-full mt-2 mr-3"></div>
                        <div class="flex-1">
                            <div class="text-sm text-gray-900">拍卖会"春季汾酒专场"报名开始</div>
                            <div class="text-xs text-gray-500 mt-1">截止时间：3月15日 · 3小时前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="grid grid-cols-4 py-2">
            <button class="flex flex-col items-center py-2 text-fenjiu-red">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">首页</span>
            </button>
            <button onclick="navigateTo('products.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-box text-xl mb-1"></i>
                <span class="text-xs">藏品</span>
            </button>
            <button onclick="navigateTo('orders.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-clipboard-list text-xl mb-1"></i>
                <span class="text-xs">订单</span>
            </button>
            <button onclick="navigateTo('analytics.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </div>

    <script>
        function navigateTo(page) {
            window.location.href = page;
        }
    </script>
</body>
</html>
