<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布商品 - 汾酒拍卖商家端</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'fenjiu-red': '#C41E3A',
                        'fenjiu-gold': '#D4AF37',
                        'fenjiu-dark': '#2C3E50'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="bg-black text-white text-xs px-4 py-1 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="bg-white border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="text-gray-600">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-lg font-bold text-fenjiu-dark">发布商品</h1>
            <button onclick="saveDraft()" class="text-fenjiu-red text-sm">保存草稿</button>
        </div>
    </div>

    <!-- 表单内容 -->
    <div class="px-4 py-4 pb-20">
        <form class="space-y-6">
            <!-- 商品图片 -->
            <div class="bg-white rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-3">
                    <i class="fas fa-image text-fenjiu-red mr-2"></i>商品图片
                    <span class="text-red-500">*</span>
                </h3>
                <div class="grid grid-cols-3 gap-3">
                    <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <div class="text-center">
                            <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                            <div class="text-xs text-gray-500">主图</div>
                        </div>
                    </div>
                    <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <div class="text-center">
                            <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                            <div class="text-xs text-gray-500">副图</div>
                        </div>
                    </div>
                    <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <div class="text-center">
                            <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                            <div class="text-xs text-gray-500">副图</div>
                        </div>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">最多上传9张图片，建议尺寸750x750px</p>
            </div>

            <!-- 基本信息 -->
            <div class="bg-white rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-4">
                    <i class="fas fa-info-circle text-fenjiu-red mr-2"></i>基本信息
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            商品名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               placeholder="请输入商品名称"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                商品分类 <span class="text-red-500">*</span>
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                                <option>请选择分类</option>
                                <option>白酒</option>
                                <option>收藏版</option>
                                <option>限量版</option>
                                <option>陈年老酒</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                品牌系列
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                                <option>请选择系列</option>
                                <option>青花瓷系列</option>
                                <option>珍藏系列</option>
                                <option>纪念系列</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            商品描述
                        </label>
                        <textarea rows="4" 
                                  placeholder="请详细描述商品特点、年份、产地等信息"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent resize-none"></textarea>
                    </div>
                </div>
            </div>

            <!-- 规格信息 -->
            <div class="bg-white rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-4">
                    <i class="fas fa-ruler text-fenjiu-red mr-2"></i>规格信息
                </h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                容量 <span class="text-red-500">*</span>
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                                <option>请选择容量</option>
                                <option>125ml</option>
                                <option>250ml</option>
                                <option>500ml</option>
                                <option>750ml</option>
                                <option>1000ml</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                酒精度
                            </label>
                            <input type="text" 
                                   placeholder="如：53%vol"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                生产年份
                            </label>
                            <input type="text" 
                                   placeholder="如：2020年"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                包装规格
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                                <option>单瓶装</option>
                                <option>礼盒装</option>
                                <option>套装</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 价格库存 -->
            <div class="bg-white rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-4">
                    <i class="fas fa-tag text-fenjiu-red mr-2"></i>价格库存
                </h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                销售价格 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                <input type="number" 
                                       placeholder="0.00"
                                       class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                库存数量 <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   placeholder="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                市场参考价
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                <input type="number" 
                                       placeholder="0.00"
                                       class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                最低起售量
                            </label>
                            <input type="number" 
                                   placeholder="1"
                                   value="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他设置 -->
            <div class="bg-white rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-4">
                    <i class="fas fa-cog text-fenjiu-red mr-2"></i>其他设置
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">立即上架</div>
                            <div class="text-sm text-gray-500">发布后立即在商城展示</div>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-fenjiu-red"></div>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">允许转为拍品</div>
                            <div class="text-sm text-gray-500">后续可将此商品转为拍卖品</div>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-fenjiu-red"></div>
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3">
        <div class="flex space-x-3">
            <button onclick="saveDraft()" class="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg font-medium">
                <i class="fas fa-save mr-2"></i>保存草稿
            </button>
            <button onclick="publishProduct()" class="flex-1 bg-fenjiu-red text-white py-3 rounded-lg font-medium">
                <i class="fas fa-rocket mr-2"></i>立即发布
            </button>
        </div>
    </div>

    <script>
        function saveDraft() {
            alert('草稿已保存');
        }

        function publishProduct() {
            if (confirm('确认发布此商品吗？')) {
                alert('商品发布成功！');
                window.location.href = 'products.html';
            }
        }
    </script>
</body>
</html>
