<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家登录 - 汾酒拍卖</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'fenjiu-red': '#C41E3A',
                        'fenjiu-gold': '#D4AF37',
                        'fenjiu-dark': '#2C3E50'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-fenjiu-red to-red-700 min-h-screen">
    <!-- 状态栏 -->
    <div class="bg-black text-white text-xs px-4 py-1 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <div class="px-6 py-8 min-h-screen flex flex-col">
        <!-- Logo区域 -->
        <div class="text-center mb-12 mt-16">
            <div class="w-24 h-24 bg-white rounded-full mx-auto mb-6 flex items-center justify-center shadow-lg">
                <i class="fas fa-wine-bottle text-4xl text-fenjiu-red"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">汾酒拍卖</h1>
            <p class="text-red-100 text-lg">商家端</p>
        </div>

        <!-- 登录表单 -->
        <div class="bg-white rounded-2xl p-8 shadow-2xl flex-1">
            <h2 class="text-2xl font-bold text-fenjiu-dark mb-8 text-center">商家登录</h2>
            
            <form class="space-y-6">
                <!-- 账号输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-fenjiu-red"></i>商家账号
                    </label>
                    <input type="text" 
                           placeholder="请输入商家账号"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent outline-none transition-all">
                </div>

                <!-- 密码输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-fenjiu-red"></i>登录密码
                    </label>
                    <div class="relative">
                        <input type="password" 
                               placeholder="请输入登录密码"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-fenjiu-red focus:border-transparent outline-none transition-all pr-12">
                        <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- 记住密码 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" class="w-4 h-4 text-fenjiu-red border-gray-300 rounded focus:ring-fenjiu-red">
                        <span class="ml-2 text-sm text-gray-600">记住密码</span>
                    </label>
                    <a href="#" class="text-sm text-fenjiu-red hover:text-red-700">忘记密码？</a>
                </div>

                <!-- 登录按钮 -->
                <button type="button" 
                        onclick="login()"
                        class="w-full bg-fenjiu-red text-white py-3 rounded-lg font-medium hover:bg-red-700 transition-colors shadow-lg">
                    <i class="fas fa-sign-in-alt mr-2"></i>立即登录
                </button>
            </form>

            <!-- 其他选项 -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-sm text-gray-600 mb-4">还没有商家账号？</p>
                    <button class="text-fenjiu-red font-medium hover:text-red-700">
                        <i class="fas fa-user-plus mr-2"></i>申请入驻
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center mt-6">
            <p class="text-red-100 text-sm">
                <i class="fas fa-shield-alt mr-2"></i>
                安全登录 · 数据加密
            </p>
        </div>
    </div>

    <script>
        function login() {
            // 模拟登录过程
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>登录中...';
            button.disabled = true;
            
            setTimeout(() => {
                // 跳转到首页
                window.location.href = 'dashboard.html';
            }, 1500);
        }

        // 密码显示/隐藏切换
        document.querySelector('.fa-eye').addEventListener('click', function() {
            const passwordInput = this.closest('.relative').querySelector('input');
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            this.classList.toggle('fa-eye');
            this.classList.toggle('fa-eye-slash');
        });
    </script>
</body>
</html>
