<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汾酒拍卖 - 高保真原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 手机框样式 */
        .mobile-frame {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            border: 8px solid #333;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            background: white;
        }

        /* 自定义样式 */
        .brand-red { color: #C8102E; }
        .bg-brand-red { background-color: #C8102E; }
        .border-brand-red { border-color: #C8102E; }
        .text-brand-red { color: #C8102E; }
        
        /* 隐藏所有页面，只显示当前激活的页面 */
        .page { display: none; }
        .page.active { display: block; }
        
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #C8102E;
            border-radius: 2px;
        }
        
        /* 拍卖倒计时动画 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .pulse-animation { animation: pulse 2s infinite; }
        
        /* 卡片悬停效果 */
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        /* Tab切换动画 */
        .collection-tab-content, .order-tab-content {
            transition: opacity 0.3s ease-in-out;
        }

        /* 按钮悬停效果 */
        .publish-type-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* 商品卡片悬停效果 */
        .product-item:hover, .auction-item:hover, .sales-order-item:hover, .auction-order-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* 图片上传区域悬停效果 */
        .aspect-square:hover {
            border-color: #C8102E;
            background-color: #fef2f2;
            transition: all 0.3s ease;
        }

        /* 状态标签样式 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-badge::before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: currentColor;
        }

        /* 页面底部内边距，避免被固定按钮遮挡 */
        .page-with-bottom-buttons {
            padding-bottom: 140px;
        }
    </style>
</head>
<body class="bg-gray-100 p-8 font-sans">
    <!-- 手机框容器 -->
    <div class="mobile-frame">
        <!-- 顶部状态栏 -->
        <div class="bg-white border-b border-gray-200 px-4 py-2 text-xs text-gray-600 flex justify-between">
            <span>9:41</span>
            <span>汾酒拍卖</span>
            <span>100% <i class="fas fa-battery-full"></i></span>
        </div>

        <!-- 主容器 -->
        <div class="w-full h-full bg-white relative overflow-y-auto">
        
        <!-- 首页 -->
        <div id="home" class="page active">
            <!-- 轮播图 -->
            <div class="relative h-48 overflow-hidden">
                <div class="carousel-container flex transition-transform duration-500 ease-in-out" id="carousel">
                    <!-- 轮播图1 -->
                    <div class="w-full flex-shrink-0 bg-gradient-to-r from-red-500 to-red-600">
                        <div class="h-48 flex items-center justify-center text-white">
                            <div class="text-center">
                                <h2 class="text-xl font-bold mb-2">汾酒专场拍卖</h2>
                                <p class="text-sm opacity-90">珍藏佳酿，竞拍开始</p>
                            </div>
                        </div>
                    </div>
                    <!-- 轮播图2 -->
                    <div class="w-full flex-shrink-0 bg-gradient-to-r from-blue-500 to-blue-600">
                        <div class="h-48 flex items-center justify-center text-white">
                            <div class="text-center">
                                <h2 class="text-xl font-bold mb-2">青花系列上新</h2>
                                <p class="text-sm opacity-90">经典传承，品质保证</p>
                            </div>
                        </div>
                    </div>
                    <!-- 轮播图3 -->
                    <div class="w-full flex-shrink-0 bg-gradient-to-r from-green-500 to-green-600">
                        <div class="h-48 flex items-center justify-center text-white">
                            <div class="text-center">
                                <h2 class="text-xl font-bold mb-2">限时特惠</h2>
                                <p class="text-sm opacity-90">精选好酒，优惠价格</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    <div class="w-2 h-2 bg-white rounded-full carousel-dot active" data-slide="0"></div>
                    <div class="w-2 h-2 bg-white opacity-50 rounded-full carousel-dot" data-slide="1"></div>
                    <div class="w-2 h-2 bg-white opacity-50 rounded-full carousel-dot" data-slide="2"></div>
                </div>
            </div>

            <!-- 快速入口 -->
            <div class="p-4">
                <div class="grid grid-cols-4 gap-4">
                    <div class="text-center" onclick="showPage('auction')">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-gavel text-brand-red text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-600">拍卖</span>
                    </div>
                    <div class="text-center" onclick="showPage('shop')">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-store text-blue-500 text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-600">商城</span>
                    </div>
                    <div class="text-center" onclick="showPage('publish')">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-plus text-green-500 text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-600">发布</span>
                    </div>
                    <div class="text-center" onclick="showPage('my-orders')">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-receipt text-orange-500 text-lg"></i>
                        </div>
                        <span class="text-xs text-gray-600">订单</span>
                    </div>
                </div>
            </div>

            <!-- 热门拍品 -->
            <div class="px-4 pb-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-800">热门拍品</h3>
                    <span class="text-sm text-brand-red" onclick="showPage('auction')">查看更多 <i class="fas fa-chevron-right"></i></span>
                </div>
                <div class="space-y-3">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 card-hover" onclick="showPage('auction-detail')">
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒青花30年</h4>
                                <p class="text-xs text-gray-500 mb-2">1993年珍藏版</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥8,888</span>
                                        <span class="text-xs text-gray-400 ml-1">当前价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">剩余时间</div>
                                        <div class="text-sm font-medium text-brand-red pulse-animation">02:15:30</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐商品 -->
            <div class="px-4 pb-20">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-800">推荐商品</h3>
                    <span class="text-sm text-brand-red" onclick="showPage('shop')">查看更多 <i class="fas fa-chevron-right"></i></span>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 card-hover" onclick="showPage('product-detail')">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCA3NUM0My4zNjk2IDc1IDM4IDY5LjYzMDQgMzggNjNWMzdDMzggMzAuMzY5NiA0My4zNjk2IDI1IDUwIDI1QzU2LjYzMDQgMjUgNjIgMzAuMzY5NiA2MiAzN1Y2M0M2MiA2OS42MzA0IDU2LjYzMDQgNzUgNTAgNzVaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-full h-24 rounded-lg object-cover mb-2">
                        <h4 class="font-medium text-gray-800 text-sm mb-1">汾酒老白汾</h4>
                        <p class="text-xs text-gray-500 mb-2">1998  53%vol</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-brand-red">¥299</span>
                            <span class="text-xs text-gray-400">月销1000+</span>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 card-hover">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCA3NUM0My4zNjk2IDc1IDM4IDY5LjYzMDQgMzggNjNWMzdDMzggMzAuMzY5NiA0My4zNjk2IDI1IDUwIDI1QzU2LjYzMDQgMjUgNjIgMzAuMzY5NiA2MiAzN1Y2M0M2MiA2OS42MzA0IDU2LjYzMDQgNzUgNTAgNzVaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-full h-24 rounded-lg object-cover mb-2">
                        <h4 class="font-medium text-gray-800 text-sm mb-1">汾酒竹叶青</h4>
                        <p class="text-xs text-gray-500 mb-2">2005  45%vol</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-brand-red">¥168</span>
                            <span class="text-xs text-gray-400">月销800+</span>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 card-hover">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCA3NUM0My4zNjk2IDc1IDM4IDY5LjYzMDQgMzggNjNWMzdDMzggMzAuMzY5NiA0My4zNjk2IDI1IDUwIDI1QzU2LjYzMDQgMjUgNjIgMzAuMzY5NiA2MiAzN1Y2M0M2MiA2OS42MzA0IDU2LjYzMDQgNzUgNTAgNzVaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-full h-24 rounded-lg object-cover mb-2">
                        <h4 class="font-medium text-gray-800 text-sm mb-1">汾酒青花30年</h4>
                        <p class="text-xs text-gray-500 mb-2">1993  53%vol</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-brand-red">¥888</span>
                            <span class="text-xs text-gray-400">月销200+</span>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 card-hover">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCA3NUM0My4zNjk2IDc1IDM4IDY5LjYzMDQgMzggNjNWMzdDMzggMzAuMzY5NiA0My4zNjk2IDI1IDUwIDI1QzU2LjYzMDQgMjUgNjIgMzAuMzY5NiA2MiAzN1Y2M0M2MiA2OS42MzA0IDU2LjYzMDQgNzUgNTAgNzVaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-full h-24 rounded-lg object-cover mb-2">
                        <h4 class="font-medium text-gray-800 text-sm mb-1">汾酒国藏</h4>
                        <p class="text-xs text-gray-500 mb-2">2010  52%vol</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-brand-red">¥458</span>
                            <span class="text-xs text-gray-400">月销500+</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 拍卖页面 -->
        <div id="auction" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('home')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">拍卖大厅</h1>
                <i class="fas fa-search text-gray-600"></i>
            </div>

            <!-- 拍卖状态标签 -->
            <div class="bg-white px-4 py-3 border-b border-gray-100">
                <div class="flex space-x-4">
                    <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium">正在拍卖</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">即将开始</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">已结束</button>
                </div>
            </div>

            <!-- 拍卖列表 -->
            <div class="p-4 space-y-4 pb-20">
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 card-hover" onclick="showPage('auction-detail')">
                    <div class="flex items-center justify-between mb-3">
                        <span class="bg-red-100 text-brand-red px-2 py-1 rounded text-xs font-medium">拍卖中</span>
                        <span class="text-xs text-gray-500">15人参与</span>
                    </div>
                    <div class="flex">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-20 h-20 rounded-lg object-cover">
                        <div class="ml-4 flex-1">
                            <h3 class="font-semibold text-gray-800 mb-1">汾酒青花30年珍藏版</h3>
                            <p class="text-sm text-gray-500 mb-2">1993年出厂，原装正品</p>
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold text-brand-red">¥8,888</div>
                                    <div class="text-xs text-gray-400">起拍价: ¥5,000</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-xs text-gray-500">剩余时间</div>
                                    <div class="text-lg font-bold text-brand-red pulse-animation">02:15:30</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 拍卖详情页 -->
        <div id="auction-detail" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('auction')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">拍品详情</h1>
                <i class="fas fa-share-alt text-gray-600"></i>
            </div>

            <!-- 商品图片 -->
            <div class="bg-white">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMDAgMjI1QzE3My44NTggMjI1IDE1MyAxODEuNDIxIDE1MyAxMjhDMTUzIDc0LjU3ODYgMTczLjg1OCAzMSAyMDAgMzFDMjI2LjE0MiAzMSAyNDcgNzQuNTc4NiAyNDcgMTI4QzI0NyAxODEuNDIxIDIyNi4xNDIgMjI1IDIwMCAyMjVaIiBmaWxsPSIjQzgxMDJFIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMjcwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2NjY2IiBmb250LXNpemU9IjE0Ij7msrTphpLpnZLoi7EzMOW5tDwvdGV4dD4KPC9zdmc+" alt="汾酒青花30年" class="w-full h-64 object-cover">
            </div>

            <!-- 拍卖信息 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <div class="flex items-center justify-between mb-3">
                    <span class="bg-red-100 text-brand-red px-3 py-1 rounded-full text-sm font-medium pulse-animation">拍卖中</span>
                    <span class="text-sm text-gray-500">15人参与竞拍</span>
                </div>
                <h1 class="text-xl font-bold text-gray-800 mb-2">汾酒青花30年珍藏版</h1>
                <p class="text-gray-600 mb-4">1993年出厂，原装正品，收藏价值极高</p>

                <!-- 价格信息 -->
                <div class="bg-red-50 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-3xl font-bold text-brand-red">¥8,888</div>
                            <div class="text-sm text-gray-500">当前价格</div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-brand-red pulse-animation">02:15:30</div>
                            <div class="text-sm text-gray-500">剩余时间</div>
                        </div>
                    </div>
                </div>

                <!-- 拍卖信息 -->
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-lg font-semibold text-gray-800">¥5,000</div>
                        <div class="text-xs text-gray-500">起拍价</div>
                    </div>
                    <div>
                        <div class="text-lg font-semibold text-gray-800">¥100</div>
                        <div class="text-xs text-gray-500">加价幅度</div>
                    </div>
                    <div>
                        <div class="text-lg font-semibold text-gray-800">15</div>
                        <div class="text-xs text-gray-500">出价次数</div>
                    </div>
                </div>
            </div>

            <!-- 出价记录 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <h3 class="font-semibold text-gray-800 mb-3">出价记录</h3>
                <div class="space-y-3 max-h-32 overflow-y-auto custom-scrollbar">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-gray-500 text-xs"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">用户***8888</div>
                                <div class="text-xs text-gray-500">2分钟前</div>
                            </div>
                        </div>
                        <div class="text-brand-red font-semibold">¥8,888</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-gray-500 text-xs"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">用户***6666</div>
                                <div class="text-xs text-gray-500">5分钟前</div>
                            </div>
                        </div>
                        <div class="text-gray-600 font-semibold">¥8,788</div>
                    </div>
                </div>
            </div>

            <!-- 商品详情 -->
            <div class="bg-white p-4 mb-20">
                <h3 class="font-semibold text-gray-800 mb-3">商品详情</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-500">品牌</span>
                        <span class="text-gray-800">汾酒</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">系列</span>
                        <span class="text-gray-800">青花系列</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">年份</span>
                        <span class="text-gray-800">1993年</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">度数</span>
                        <span class="text-gray-800">53度</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">容量</span>
                        <span class="text-gray-800">500ml</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">包装</span>
                        <span class="text-gray-800">礼盒装</span>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="absolute bottom-16 left-0 right-0 bg-white border-t border-gray-200 p-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-lg font-medium">
                        <i class="fas fa-heart mr-2"></i>收藏
                    </button>
                    <button class="flex-1 bg-brand-red text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-gavel mr-2"></i>出价 ¥8,988
                    </button>
                </div>
            </div>
        </div>

        <!-- 我的页面 -->
        <div id="profile" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('home')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">我的</h1>
                <i class="fas fa-cog text-gray-600"></i>
            </div>

            <!-- 用户信息 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user text-gray-500 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-800 mb-1">汾酒收藏家</h3>
                        <p class="text-sm text-gray-500 mb-2">手机号：138****8888</p>
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium">已实名</span>
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs font-medium ml-2">VIP会员</span>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <div class="grid grid-cols-4 gap-4 text-center">
                    <div>
                        <div class="text-xl font-bold text-gray-800">12</div>
                        <div class="text-xs text-gray-500">我的藏品</div>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-800">5</div>
                        <div class="text-xs text-gray-500">参与拍卖</div>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-800">3</div>
                        <div class="text-xs text-gray-500">成功竞拍</div>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-800">8</div>
                        <div class="text-xs text-gray-500">我的奖品</div>
                    </div>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="bg-white mt-2">
                <div class="p-4 border-b border-gray-100 flex items-center" onclick="showPage('my-collection')">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-gem text-brand-red"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">我的藏品</h4>
                        <p class="text-xs text-gray-500">管理我的汾酒收藏</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="p-4 border-b border-gray-100 flex items-center" onclick="showPage('my-orders')">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-receipt text-blue-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">我的订单</h4>
                        <p class="text-xs text-gray-500">查看购买和销售记录</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="p-4 border-b border-gray-100 flex items-center" onclick="showPage('auction-records')">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-gavel text-green-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">参拍记录</h4>
                        <p class="text-xs text-gray-500">查看拍卖参与历史</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="p-4 border-b border-gray-100 flex items-center" onclick="showPage('my-rewards')">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-gift text-purple-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">我的奖品</h4>
                        <p class="text-xs text-gray-500">查看获得的奖品</p>
                    </div>
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2">3</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 其他功能 -->
            <div class="bg-white mt-2 mb-20">
                <div class="p-4 border-b border-gray-100 flex items-center">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-shield-alt text-gray-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">实名认证</h4>
                        <p class="text-xs text-gray-500">提升账户安全性</p>
                    </div>
                    <span class="text-green-500 text-sm">已认证</span>
                    <i class="fas fa-chevron-right text-gray-400 ml-2"></i>
                </div>

                <div class="p-4 border-b border-gray-100 flex items-center">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-headset text-gray-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">客服中心</h4>
                        <p class="text-xs text-gray-500">在线客服为您服务</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="p-4 flex items-center">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-info-circle text-gray-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">关于我们</h4>
                        <p class="text-xs text-gray-500">了解汾酒拍卖平台</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 我的藏品页面 -->
        <div id="my-collection" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('profile')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">我的藏品</h1>
                <i class="fas fa-plus text-brand-red" onclick="showPage('publish')"></i>
            </div>

            <!-- 主要Tab切换 -->
            <div class="bg-white px-4 py-3 border-b border-gray-100">
                <div class="flex space-x-4">
                    <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium collection-main-tab active" data-tab="products" onclick="switchCollectionMainTab('products')">商品</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm collection-main-tab" data-tab="auctions" onclick="switchCollectionMainTab('auctions')">拍品</button>
                </div>
            </div>

            <!-- 商品Tab内容 -->
            <div id="products-tab" class="collection-tab-content">
                <!-- 商品状态标签 -->
                <div class="bg-white px-4 py-3 border-b border-gray-100">
                    <div class="flex space-x-4 overflow-x-auto">
                        <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium whitespace-nowrap product-status-tab active" data-status="all" onclick="switchProductStatus('all')">全部</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap product-status-tab" data-status="selling" onclick="switchProductStatus('selling')">在售</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap product-status-tab" data-status="draft" onclick="switchProductStatus('draft')">草稿</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap product-status-tab" data-status="sold" onclick="switchProductStatus('sold')">已售出</button>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="p-4 space-y-4 pb-20" id="products-list">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 product-item" data-status="selling" data-product-id="prod1">
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium status-badge">在售</span>
                            <div class="flex space-x-2">
                                <button class="text-blue-500 text-sm" onclick="viewProductDetail('prod1')">详情</button>
                                <button class="text-brand-red text-sm" onclick="editProduct('prod1')">编辑</button>
                                <button class="text-gray-500 text-sm" onclick="toggleProductStatus('prod1', 'off')">下架</button>
                            </div>
                        </div>
                        <div class="flex" onclick="viewProductDetail('prod1')">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-20 h-20 rounded-lg object-cover">
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold text-gray-800 mb-1">汾酒老白汾</h3>
                                <p class="text-sm text-gray-500 mb-2">1998  53%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥299</span>
                                        <span class="text-xs text-gray-400 ml-1">售价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">浏览量</div>
                                        <div class="text-sm font-medium text-gray-800">128</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 product-item" data-status="draft" data-product-id="prod2">
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs font-medium status-badge">草稿</span>
                            <div class="flex space-x-2">
                                <button class="text-blue-500 text-sm" onclick="viewProductDetail('prod2')">详情</button>
                                <button class="text-brand-red text-sm" onclick="editProduct('prod2')">编辑</button>
                                <button class="text-green-500 text-sm" onclick="toggleProductStatus('prod2', 'on')">上架</button>
                            </div>
                        </div>
                        <div class="flex" onclick="viewProductDetail('prod2')">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-20 h-20 rounded-lg object-cover">
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold text-gray-800 mb-1">汾酒竹叶青</h3>
                                <p class="text-sm text-gray-500 mb-2">2005  45%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥168</span>
                                        <span class="text-xs text-gray-400 ml-1">售价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">浏览量</div>
                                        <div class="text-sm font-medium text-gray-800">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 拍品Tab内容 -->
            <div id="auctions-tab" class="collection-tab-content" style="display: none;">
                <!-- 拍品状态标签 -->
                <div class="bg-white px-4 py-3 border-b border-gray-100">
                    <div class="flex space-x-4 overflow-x-auto">
                        <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium whitespace-nowrap auction-status-tab active" data-status="all" onclick="switchAuctionStatus('all')">全部</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap auction-status-tab" data-status="ongoing" onclick="switchAuctionStatus('ongoing')">拍卖中</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap auction-status-tab" data-status="upcoming" onclick="switchAuctionStatus('upcoming')">即将开始</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap auction-status-tab" data-status="ended" onclick="switchAuctionStatus('ended')">已结束</button>
                    </div>
                </div>

                <!-- 拍品列表 -->
                <div class="p-4 space-y-4 pb-20" id="auctions-list">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 auction-item" data-status="ongoing">
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-red-100 text-brand-red px-2 py-1 rounded text-xs font-medium pulse-animation">拍卖中</span>
                            <div class="flex space-x-2">
                                <button class="text-blue-500 text-sm" onclick="viewAuctionDetail('auction1')">详情</button>
                                <button class="text-brand-red text-sm" onclick="editAuction('auction1')">编辑</button>
                            </div>
                        </div>
                        <div class="flex" onclick="viewAuctionDetail('auction1')">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-20 h-20 rounded-lg object-cover">
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold text-gray-800 mb-1">汾酒青花30年</h3>
                                <p class="text-sm text-gray-500 mb-2">1993  53%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥8,888</span>
                                        <span class="text-xs text-gray-400 ml-1">当前价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">剩余时间</div>
                                        <div class="text-sm font-medium text-brand-red pulse-animation">02:15:30</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 auction-item" data-status="upcoming">
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs font-medium">即将开始</span>
                            <div class="flex space-x-2">
                                <button class="text-blue-500 text-sm" onclick="viewAuctionDetail('auction2')">详情</button>
                                <button class="text-brand-red text-sm" onclick="editAuction('auction2')">编辑</button>
                            </div>
                        </div>
                        <div class="flex" onclick="viewAuctionDetail('auction2')">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-20 h-20 rounded-lg object-cover">
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold text-gray-800 mb-1">汾酒国藏</h3>
                                <p class="text-sm text-gray-500 mb-2">2010  52%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥500</span>
                                        <span class="text-xs text-gray-400 ml-1">起拍价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">开始时间</div>
                                        <div class="text-sm font-medium text-gray-800">12-20 10:00</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布页面 -->
        <div id="publish" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('home')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">发布商品</h1>
                <button class="text-brand-red text-sm" onclick="saveDraft()">保存草稿</button>
            </div>

            <!-- 发布类型选择 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <h3 class="font-semibold text-gray-800 mb-3">发布类型</h3>
                <div class="grid grid-cols-2 gap-3">
                    <button class="p-4 border-2 border-brand-red bg-red-50 rounded-lg text-center publish-type-btn active" data-type="direct" onclick="switchPublishType('direct')">
                        <i class="fas fa-store text-brand-red text-2xl mb-2"></i>
                        <div class="font-medium text-brand-red">直接销售</div>
                        <div class="text-xs text-gray-500 mt-1">固定价格出售</div>
                    </button>
                    <button class="p-4 border border-gray-300 rounded-lg text-center publish-type-btn" data-type="auction" onclick="switchPublishType('auction')">
                        <i class="fas fa-gavel text-gray-500 text-2xl mb-2"></i>
                        <div class="font-medium text-gray-600">拍卖销售</div>
                        <div class="text-xs text-gray-500 mt-1">竞价拍卖出售</div>
                    </button>
                </div>
            </div>

            <!-- 商品信息表单 -->
            <div class="bg-white p-4 space-y-4 page-with-bottom-buttons">
                <!-- 商品图片 -->
                <div>
                    <h4 class="font-medium text-gray-800 mb-2">
                        <i class="fas fa-image text-brand-red mr-2"></i>商品图片
                        <span class="text-red-500">*</span>
                    </h4>
                    <div class="grid grid-cols-3 gap-3">
                        <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:border-brand-red" onclick="uploadImage()">
                            <div class="text-center">
                                <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                                <div class="text-xs text-gray-500">主图</div>
                            </div>
                        </div>
                        <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:border-brand-red" onclick="uploadImage()">
                            <div class="text-center">
                                <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                                <div class="text-xs text-gray-500">副图</div>
                            </div>
                        </div>
                        <div class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:border-brand-red" onclick="uploadImage()">
                            <div class="text-center">
                                <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                                <div class="text-xs text-gray-500">副图</div>
                            </div>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">最多上传9张图片，建议尺寸750x750px</p>
                </div>

                <!-- 基本信息 -->
                <div>
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-info-circle text-brand-red mr-2"></i>基本信息
                    </h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                商品名称 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" placeholder="请输入商品名称" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    商品分类 <span class="text-red-500">*</span>
                                </label>
                                <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                    <option>请选择分类</option>
                                    <option>白酒</option>
                                    <option>收藏版</option>
                                    <option>限量版</option>
                                    <option>陈年老酒</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    品牌系列
                                </label>
                                <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                    <option>请选择系列</option>
                                    <option>青花瓷系列</option>
                                    <option>珍藏系列</option>
                                    <option>纪念系列</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                商品描述
                            </label>
                            <textarea rows="4" placeholder="请详细描述商品特点、年份、产地等信息" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent resize-none"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 规格信息 -->
                <div>
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-ruler text-brand-red mr-2"></i>规格信息
                    </h4>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    容量 <span class="text-red-500">*</span>
                                </label>
                                <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                    <option>请选择容量</option>
                                    <option>125ml</option>
                                    <option>250ml</option>
                                    <option>500ml</option>
                                    <option>750ml</option>
                                    <option>1000ml</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    酒精度
                                </label>
                                <input type="text" placeholder="如：53%vol" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    生产年份
                                </label>
                                <input type="text" placeholder="如：2020年" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    包装规格
                                </label>
                                <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                    <option>单瓶装</option>
                                    <option>礼盒装</option>
                                    <option>套装</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格设置 - 直接销售 -->
                <div id="direct-price-section">
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-tag text-brand-red mr-2"></i>价格库存
                    </h4>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    销售价格 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                    <input type="number" placeholder="0.00" class="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    库存数量 <span class="text-red-500">*</span>
                                </label>
                                <input type="number" placeholder="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    市场参考价
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                    <input type="number" placeholder="0.00" class="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    最低起售量
                                </label>
                                <input type="number" placeholder="1" value="1" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 拍卖设置 - 拍卖销售 -->
                <div id="auction-price-section" style="display: none;">
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-gavel text-brand-red mr-2"></i>拍卖设置
                    </h4>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    起拍价 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                    <input type="number" placeholder="0.00" class="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    加价幅度 <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                    <input type="number" placeholder="100" class="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    拍卖开始时间 <span class="text-red-500">*</span>
                                </label>
                                <input type="datetime-local" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    拍卖结束时间 <span class="text-red-500">*</span>
                                </label>
                                <input type="datetime-local" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                保留价（可选）
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                                <input type="number" placeholder="设置最低成交价" class="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-transparent">
                            </div>
                            <p class="text-xs text-gray-500 mt-1">低于保留价的出价不会成交</p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 底部操作按钮 -->
            <div class="absolute bottom-16 left-0 right-0 bg-white border-t border-gray-200 p-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-lg font-medium" onclick="saveDraft()">
                        <i class="fas fa-save mr-2"></i>保存草稿
                    </button>
                    <button class="flex-1 bg-brand-red text-white py-3 rounded-lg font-medium" onclick="publishProduct()">
                        <i class="fas fa-upload mr-2"></i>立即发布
                    </button>
                </div>
            </div>
        </div>

        <!-- 商品详情页面 -->
        <div id="product-detail" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('my-collection')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">商品详情</h1>
                <i class="fas fa-edit text-brand-red" onclick="showPage('product-edit')"></i>
            </div>

            <!-- 商品图片 -->
            <div class="bg-white">
                <div class="aspect-square bg-gray-100 flex items-center justify-center">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMDAgMTUwQzg4Ljk1NDMgMTUwIDgwIDEzMS4wNDYgODAgMTIwVjgwQzgwIDY4Ljk1NDMgODguOTU0MyA2MCAxMDAgNjBDMTExLjA0NiA2MCAxMjAgNjguOTU0MyAxMjAgODBWMTIwQzEyMCAxMzEuMDQ2IDExMS4wNDYgMTUwIDEwMCAxNTBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒老白汾" class="w-full h-full object-cover">
                </div>
            </div>

            <!-- 商品信息 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <div class="flex items-center justify-between mb-2">
                    <h1 class="text-xl font-bold text-gray-800">汾酒老白汾</h1>
                    <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium">在售</span>
                </div>
                <p class="text-gray-600 mb-4">1998年出厂，53%vol，收藏价值极高的经典汾酒</p>
                <div class="text-2xl font-bold text-brand-red mb-4">¥299</div>

                <!-- 商品规格 -->
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">容量</span>
                        <span class="text-gray-800">500ml</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">酒精度</span>
                        <span class="text-gray-800">53%vol</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">年份</span>
                        <span class="text-gray-800">1998</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">包装</span>
                        <span class="text-gray-800">礼盒装</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">库存</span>
                        <span class="text-gray-800">5件</span>
                    </div>
                </div>
            </div>

            <!-- 销售数据 -->
            <div class="bg-white p-4 border-b border-gray-100">
                <h3 class="font-medium text-gray-800 mb-3">销售数据</h3>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-lg font-bold text-brand-red">156</div>
                        <div class="text-xs text-gray-500">浏览量</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-brand-red">23</div>
                        <div class="text-xs text-gray-500">收藏量</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-bold text-brand-red">8</div>
                        <div class="text-xs text-gray-500">已售出</div>
                    </div>
                </div>
            </div>

            <!-- 底部操作按钮 -->
            <div class="absolute bottom-16 left-0 right-0 bg-white border-t border-gray-200 p-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-lg font-medium" onclick="toggleProductStatus('product1', 'off')">
                        <i class="fas fa-eye-slash mr-2"></i>下架商品
                    </button>
                    <button class="flex-1 bg-brand-red text-white py-3 rounded-lg font-medium" onclick="showPage('product-edit')">
                        <i class="fas fa-edit mr-2"></i>编辑商品
                    </button>
                </div>
            </div>
        </div>

        <!-- 商品编辑页面 -->
        <div id="product-edit" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('product-detail')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">编辑商品</h1>
                <button class="text-brand-red font-medium" onclick="saveProductEdit()">保存</button>
            </div>

            <!-- 编辑表单 -->
            <div class="p-4 space-y-6 page-with-bottom-buttons">
                <!-- 商品图片 -->
                <div>
                    <h4 class="font-medium text-gray-800 mb-3">商品图片</h4>
                    <div class="grid grid-cols-3 gap-3">
                        <div class="aspect-square bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="商品图片" class="w-full h-full object-cover rounded-lg">
                        </div>
                        <div class="aspect-square bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 cursor-pointer hover:border-brand-red hover:bg-red-50" onclick="uploadImage()">
                            <i class="fas fa-plus text-gray-400 text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- 基本信息 -->
                <div>
                    <h4 class="font-medium text-gray-800 mb-3">基本信息</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">商品名称</label>
                            <input type="text" value="汾酒老白汾" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-brand-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">商品描述</label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-brand-red">1998年出厂，53%vol，收藏价值极高的经典汾酒</textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">销售价格</label>
                            <input type="number" value="299" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-brand-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">库存数量</label>
                            <input type="number" value="5" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-red focus:border-brand-red">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部操作按钮 -->
            <div class="absolute bottom-16 left-0 right-0 bg-white border-t border-gray-200 p-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-lg font-medium" onclick="showPage('product-detail')">
                        <i class="fas fa-times mr-2"></i>取消
                    </button>
                    <button class="flex-1 bg-brand-red text-white py-3 rounded-lg font-medium" onclick="saveProductEdit()">
                        <i class="fas fa-save mr-2"></i>保存修改
                    </button>
                </div>
            </div>
        </div>

        <!-- 我的订单页面 -->
        <div id="my-orders" class="page">
            <!-- 顶部导航 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-3" onclick="showPage('home')"></i>
                <h1 class="text-lg font-semibold text-gray-800 flex-1">我的订单</h1>
                <i class="fas fa-search text-gray-600"></i>
            </div>

            <!-- 订单类型Tab切换 -->
            <div class="bg-white px-4 py-3 border-b border-gray-100">
                <div class="flex space-x-4">
                    <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium order-type-tab active" data-type="sales" onclick="switchOrderType('sales')">销售订单</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm order-type-tab" data-type="auctions" onclick="switchOrderType('auctions')">拍卖订单</button>
                </div>
            </div>

            <!-- 销售订单Tab内容 -->
            <div id="sales-orders-tab" class="order-tab-content">
                <!-- 销售订单状态标签 -->
                <div class="bg-white px-4 py-3 border-b border-gray-100">
                    <div class="flex space-x-4 overflow-x-auto">
                        <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium whitespace-nowrap sales-status-tab active" data-status="all" onclick="switchSalesStatus('all')">全部</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap sales-status-tab" data-status="pending" onclick="switchSalesStatus('pending')">待付款</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap sales-status-tab" data-status="shipping" onclick="switchSalesStatus('shipping')">待发货</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap sales-status-tab" data-status="receiving" onclick="switchSalesStatus('receiving')">待收货</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap sales-status-tab" data-status="completed" onclick="switchSalesStatus('completed')">已完成</button>
                    </div>
                </div>

                <!-- 销售订单列表 -->
                <div class="p-4 space-y-4 pb-20" id="sales-orders-list">
                    <!-- 销售订单1 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 sales-order-item" data-status="completed">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-500">订单号：FJ202312150001</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium">已完成</span>
                        </div>
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒老白汾</h4>
                                <p class="text-xs text-gray-500 mb-2">1998  53%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥299</span>
                                        <span class="text-xs text-gray-400 ml-1">x1</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">2023-12-15</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end mt-3 space-x-2">
                            <button class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg text-sm">再次购买</button>
                            <button class="px-4 py-2 bg-brand-red text-white rounded-lg text-sm">评价</button>
                        </div>
                    </div>

                    <!-- 销售订单2 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 sales-order-item" data-status="receiving">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-500">订单号：FJ202312140002</span>
                            <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs font-medium">待收货</span>
                        </div>
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒竹叶青</h4>
                                <p class="text-xs text-gray-500 mb-2">2005  45%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥168</span>
                                        <span class="text-xs text-gray-400 ml-1">x2</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">2023-12-14</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end mt-3 space-x-2">
                            <button class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg text-sm">查看物流</button>
                            <button class="px-4 py-2 bg-brand-red text-white rounded-lg text-sm">确认收货</button>
                        </div>
                    </div>

                    <!-- 销售订单3 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 sales-order-item" data-status="pending">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-500">订单号：FJ202312130004</span>
                            <span class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs font-medium">待付款</span>
                        </div>
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒国藏</h4>
                                <p class="text-xs text-gray-500 mb-2">2010  52%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥588</span>
                                        <span class="text-xs text-gray-400 ml-1">x1</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">2023-12-13</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end mt-3 space-x-2">
                            <button class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg text-sm">取消订单</button>
                            <button class="px-4 py-2 bg-brand-red text-white rounded-lg text-sm">立即付款</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 拍卖订单Tab内容 -->
            <div id="auction-orders-tab" class="order-tab-content" style="display: none;">
                <!-- 拍卖订单状态标签 -->
                <div class="bg-white px-4 py-3 border-b border-gray-100">
                    <div class="flex space-x-4 overflow-x-auto">
                        <button class="px-4 py-2 bg-brand-red text-white rounded-full text-sm font-medium whitespace-nowrap auction-status-tab active" data-status="all" onclick="switchAuctionOrderStatus('all')">全部</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap auction-status-tab" data-status="won" onclick="switchAuctionOrderStatus('won')">竞拍成功</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap auction-status-tab" data-status="failed" onclick="switchAuctionOrderStatus('failed')">竞拍失败</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap auction-status-tab" data-status="ongoing" onclick="switchAuctionOrderStatus('ongoing')">参与中</button>
                    </div>
                </div>

                <!-- 拍卖订单列表 -->
                <div class="p-4 space-y-4 pb-20" id="auction-orders-list">
                    <!-- 拍卖订单1 - 竞拍成功 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 auction-order-item" data-status="won">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-500">拍卖订单：FJ202312130003</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium">竞拍成功</span>
                        </div>
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒青花30年</h4>
                                <p class="text-xs text-gray-500 mb-2">1993  53%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥8,888</span>
                                        <span class="text-xs text-gray-400 ml-1">成交价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">2023-12-13</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end mt-3 space-x-2">
                            <button class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg text-sm">查看详情</button>
                            <button class="px-4 py-2 bg-brand-red text-white rounded-lg text-sm">立即付款</button>
                        </div>
                    </div>

                    <!-- 拍卖订单2 - 参与中 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 auction-order-item" data-status="ongoing">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-500">拍卖订单：FJ202312120005</span>
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs font-medium pulse-animation">参与中</span>
                        </div>
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒珍藏版</h4>
                                <p class="text-xs text-gray-500 mb-2">2000  50%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-brand-red">¥1,200</span>
                                        <span class="text-xs text-gray-400 ml-1">我的出价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">剩余时间</div>
                                        <div class="text-sm font-medium text-brand-red pulse-animation">01:23:45</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end mt-3 space-x-2">
                            <button class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg text-sm">查看详情</button>
                            <button class="px-4 py-2 bg-brand-red text-white rounded-lg text-sm">继续出价</button>
                        </div>
                    </div>

                    <!-- 拍卖订单3 - 竞拍失败 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 auction-order-item" data-status="failed">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-500">拍卖订单：FJ202312110006</span>
                            <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs font-medium">竞拍失败</span>
                        </div>
                        <div class="flex">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzNS41ODE3IDYwIDMyIDU2LjQxODMgMzIgNTJWMjhDMzIgMjMuNTgxNyAzNS41ODE3IDIwIDQwIDIwQzQ0LjQxODMgMjAgNDggMjMuNTgxNyA0OCAyOFY1MkM0OCA1Ni40MTgzIDQ0LjQxODMgNjAgNDAgNjBaIiBmaWxsPSIjQzgxMDJFIi8+Cjwvc3ZnPgo=" alt="汾酒" class="w-16 h-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-gray-800 mb-1">汾酒老白汾</h4>
                                <p class="text-xs text-gray-500 mb-2">1995  53%vol</p>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-lg font-bold text-gray-500">¥800</span>
                                        <span class="text-xs text-gray-400 ml-1">我的出价</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500">成交价</div>
                                        <div class="text-sm font-medium text-gray-800">¥950</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end mt-3 space-x-2">
                            <button class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg text-sm">查看详情</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm">再次关注</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200">
            <div class="flex">
                <button class="flex-1 py-3 text-center" onclick="showPage('home')" id="nav-home">
                    <i class="fas fa-home text-xl mb-1 text-brand-red"></i>
                    <div class="text-xs text-brand-red">首页</div>
                </button>
                <button class="flex-1 py-3 text-center" onclick="showPage('auction')" id="nav-auction">
                    <i class="fas fa-gavel text-xl mb-1 text-gray-400"></i>
                    <div class="text-xs text-gray-400">拍卖</div>
                </button>
                <button class="flex-1 py-3 text-center" onclick="showPage('publish')" id="nav-publish">
                    <i class="fas fa-plus-circle text-xl mb-1 text-gray-400"></i>
                    <div class="text-xs text-gray-400">发布</div>
                </button>
                <button class="flex-1 py-3 text-center" onclick="showPage('shop')" id="nav-shop">
                    <i class="fas fa-store text-xl mb-1 text-gray-400"></i>
                    <div class="text-xs text-gray-400">商城</div>
                </button>
                <button class="flex-1 py-3 text-center" onclick="showPage('profile')" id="nav-profile">
                    <i class="fas fa-user text-xl mb-1 text-gray-400"></i>
                    <div class="text-xs text-gray-400">我的</div>
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript 交互功能 -->
    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // 更新底部导航状态
            updateNavigation(pageId);
        }

        // 更新底部导航状态
        function updateNavigation(activePageId) {
            const navButtons = document.querySelectorAll('[id^="nav-"]');
            navButtons.forEach(button => {
                const icon = button.querySelector('i');
                const text = button.querySelector('div');

                // 重置所有按钮状态
                icon.className = icon.className.replace('text-brand-red', 'text-gray-400');
                text.className = text.className.replace('text-brand-red', 'text-gray-400');
            });

            // 设置当前激活按钮状态
            const activeNav = document.getElementById(`nav-${activePageId}`);
            if (activeNav) {
                const icon = activeNav.querySelector('i');
                const text = activeNav.querySelector('div');
                icon.className = icon.className.replace('text-gray-400', 'text-brand-red');
                text.className = text.className.replace('text-gray-400', 'text-brand-red');
            }
        }

        // 拍卖倒计时功能
        function updateCountdown() {
            const countdownElements = document.querySelectorAll('.pulse-animation');
            countdownElements.forEach(element => {
                if (element.textContent.includes(':')) {
                    // 简单的倒计时模拟
                    const currentTime = element.textContent;
                    const parts = currentTime.split(':');
                    let hours = parseInt(parts[0]);
                    let minutes = parseInt(parts[1]);
                    let seconds = parseInt(parts[2]);

                    seconds--;
                    if (seconds < 0) {
                        seconds = 59;
                        minutes--;
                        if (minutes < 0) {
                            minutes = 59;
                            hours--;
                            if (hours < 0) {
                                hours = 0;
                                minutes = 0;
                                seconds = 0;
                            }
                        }
                    }

                    element.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            });
        }

        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);

        // 轮播图功能
        let currentSlide = 0;
        const totalSlides = 3;
        let carouselInterval;

        function showSlide(index) {
            const carousel = document.getElementById('carousel');
            const dots = document.querySelectorAll('.carousel-dot');

            if (carousel) {
                carousel.style.transform = `translateX(-${index * 100}%)`;

                // 更新指示点
                dots.forEach((dot, i) => {
                    if (i === index) {
                        dot.classList.add('active');
                        dot.style.opacity = '1';
                    } else {
                        dot.classList.remove('active');
                        dot.style.opacity = '0.5';
                    }
                });
            }
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        function startCarousel() {
            carouselInterval = setInterval(nextSlide, 3000); // 每3秒切换
        }

        function stopCarousel() {
            if (carouselInterval) {
                clearInterval(carouselInterval);
            }
        }

        // 点击指示点切换
        function initCarouselDots() {
            const dots = document.querySelectorAll('.carousel-dot');
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    showSlide(currentSlide);
                    stopCarousel();
                    startCarousel(); // 重新开始自动播放
                });
            });
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            showPage('home');
            initCarouselDots();
            startCarousel();
        });

        // 我的藏品页面功能
        function switchCollectionMainTab(tab) {
            // 切换主Tab按钮样式
            document.querySelectorAll('.collection-main-tab').forEach(btn => {
                btn.classList.remove('active', 'bg-brand-red', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            const activeBtn = document.querySelector(`[data-tab="${tab}"]`);
            activeBtn.classList.add('active', 'bg-brand-red', 'text-white');
            activeBtn.classList.remove('bg-gray-100', 'text-gray-600');

            // 切换内容显示
            document.querySelectorAll('.collection-tab-content').forEach(content => {
                content.style.display = 'none';
            });

            document.getElementById(`${tab}-tab`).style.display = 'block';
        }

        function switchProductStatus(status) {
            // 切换状态按钮样式
            document.querySelectorAll('.product-status-tab').forEach(btn => {
                btn.classList.remove('active', 'bg-brand-red', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            const activeBtn = document.querySelector(`.product-status-tab[data-status="${status}"]`);
            activeBtn.classList.add('active', 'bg-brand-red', 'text-white');
            activeBtn.classList.remove('bg-gray-100', 'text-gray-600');

            // 过滤商品列表
            const items = document.querySelectorAll('.product-item');
            items.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function switchAuctionStatus(status) {
            // 切换状态按钮样式
            document.querySelectorAll('.auction-status-tab').forEach(btn => {
                btn.classList.remove('active', 'bg-brand-red', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            const activeBtn = document.querySelector(`.auction-status-tab[data-status="${status}"]`);
            activeBtn.classList.add('active', 'bg-brand-red', 'text-white');
            activeBtn.classList.remove('bg-gray-100', 'text-gray-600');

            // 过滤拍品列表
            const items = document.querySelectorAll('.auction-item');
            items.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 商品操作功能
        function viewProductDetail(productId) {
            showPage('product-detail');
        }

        function editProduct(productId) {
            showPage('product-edit');
        }

        function toggleProductStatus(productId, action) {
            const actionText = action === 'on' ? '上架' : '下架';
            if (confirm(`确认${actionText}此商品吗？`)) {
                // 更新商品状态显示
                const productItem = document.querySelector(`[data-product-id="${productId}"]`);
                if (productItem) {
                    const statusBadge = productItem.querySelector('.status-badge');
                    if (action === 'off') {
                        statusBadge.className = 'bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs font-medium status-badge';
                        statusBadge.textContent = '已下架';
                        productItem.dataset.status = 'draft';
                    } else {
                        statusBadge.className = 'bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium status-badge';
                        statusBadge.textContent = '在售';
                        productItem.dataset.status = 'selling';
                    }
                }
                alert(`商品已${actionText}`);
                showPage('my-collection');
            }
        }

        function saveProductEdit() {
            if (confirm('确认保存修改吗？')) {
                alert('商品信息已更新');
                showPage('product-detail');
            }
        }

        // 拍品操作功能
        function viewAuctionDetail(auctionId) {
            alert(`查看拍品详情: ${auctionId}`);
        }

        function editAuction(auctionId) {
            alert(`编辑拍品: ${auctionId}`);
        }

        // 发布页面功能
        function switchPublishType(type) {
            // 切换发布类型按钮样式
            document.querySelectorAll('.publish-type-btn').forEach(btn => {
                btn.classList.remove('active', 'border-brand-red', 'bg-red-50');
                btn.classList.add('border-gray-300');

                const icon = btn.querySelector('i');
                const title = btn.querySelector('.font-medium');
                icon.classList.remove('text-brand-red');
                icon.classList.add('text-gray-500');
                title.classList.remove('text-brand-red');
                title.classList.add('text-gray-600');
            });

            const activeBtn = document.querySelector(`[data-type="${type}"]`);
            activeBtn.classList.add('active', 'border-brand-red', 'bg-red-50');
            activeBtn.classList.remove('border-gray-300');

            const activeIcon = activeBtn.querySelector('i');
            const activeTitle = activeBtn.querySelector('.font-medium');
            activeIcon.classList.add('text-brand-red');
            activeIcon.classList.remove('text-gray-500');
            activeTitle.classList.add('text-brand-red');
            activeTitle.classList.remove('text-gray-600');

            // 切换价格设置区域
            if (type === 'direct') {
                document.getElementById('direct-price-section').style.display = 'block';
                document.getElementById('auction-price-section').style.display = 'none';
            } else {
                document.getElementById('direct-price-section').style.display = 'none';
                document.getElementById('auction-price-section').style.display = 'block';
            }
        }

        function uploadImage() {
            alert('选择图片上传');
        }

        function saveDraft() {
            alert('草稿已保存');
        }

        function publishProduct() {
            if (confirm('确认发布此商品吗？')) {
                alert('商品发布成功！');
                showPage('my-collection');
            }
        }

        // 订单页面功能
        function switchOrderType(type) {
            // 切换订单类型按钮样式
            document.querySelectorAll('.order-type-tab').forEach(btn => {
                btn.classList.remove('active', 'bg-brand-red', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            const activeBtn = document.querySelector(`[data-type="${type}"]`);
            activeBtn.classList.add('active', 'bg-brand-red', 'text-white');
            activeBtn.classList.remove('bg-gray-100', 'text-gray-600');

            // 切换内容显示
            document.querySelectorAll('.order-tab-content').forEach(content => {
                content.style.display = 'none';
            });

            document.getElementById(`${type}-orders-tab`).style.display = 'block';
        }

        function switchSalesStatus(status) {
            // 切换销售订单状态按钮样式
            document.querySelectorAll('.sales-status-tab').forEach(btn => {
                btn.classList.remove('active', 'bg-brand-red', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            const activeBtn = document.querySelector(`.sales-status-tab[data-status="${status}"]`);
            activeBtn.classList.add('active', 'bg-brand-red', 'text-white');
            activeBtn.classList.remove('bg-gray-100', 'text-gray-600');

            // 过滤销售订单列表
            const items = document.querySelectorAll('.sales-order-item');
            items.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function switchAuctionOrderStatus(status) {
            // 切换拍卖订单状态按钮样式
            document.querySelectorAll('.auction-status-tab').forEach(btn => {
                btn.classList.remove('active', 'bg-brand-red', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            const activeBtn = document.querySelector(`.auction-status-tab[data-status="${status}"]`);
            activeBtn.classList.add('active', 'bg-brand-red', 'text-white');
            activeBtn.classList.remove('bg-gray-100', 'text-gray-600');

            // 过滤拍卖订单列表
            const items = document.querySelectorAll('.auction-order-item');
            items.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 模拟数据加载
        function simulateDataLoading() {
            // 这里可以添加数据加载的模拟逻辑
            console.log('数据加载完成');
        }

        // 页面加载完成后执行
        window.addEventListener('load', simulateDataLoading);
    </script>
        </div>
    </div>
</body>
</html>
