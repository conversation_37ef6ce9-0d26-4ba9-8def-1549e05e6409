<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 汾酒拍卖商家端</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'fenjiu-red': '#C41E3A',
                        'fenjiu-gold': '#D4AF37',
                        'fenjiu-dark': '#2C3E50'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="bg-black text-white text-xs px-4 py-1 flex justify-between items-center">
        <span>9:41</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="bg-white border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <button onclick="history.back()" class="text-gray-600">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-lg font-bold text-fenjiu-dark">订单管理</h1>
            <button class="text-fenjiu-red">
                <i class="fas fa-search text-xl"></i>
            </button>
        </div>
    </div>

    <!-- 订单类型切换 -->
    <div class="bg-white px-4 py-3 border-b border-gray-200">
        <div class="flex space-x-1">
            <button onclick="switchTab('sales')" id="sales-tab" class="px-4 py-2 bg-fenjiu-red text-white rounded-full text-sm">
                <i class="fas fa-shopping-cart mr-1"></i>销售订单
            </button>
            <button onclick="switchTab('auction')" id="auction-tab" class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">
                <i class="fas fa-gavel mr-1"></i>拍卖订单
            </button>
        </div>
    </div>

    <!-- 状态筛选 -->
    <div class="bg-white px-4 py-3 border-b border-gray-200">
        <div class="flex space-x-1 overflow-x-auto">
            <button class="px-3 py-1 bg-fenjiu-red text-white rounded-full text-sm whitespace-nowrap">全部</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">待付款</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">已付款</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">已完成</button>
            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">已取消</button>
        </div>
    </div>

    <!-- 销售订单列表 -->
    <div id="sales-orders" class="px-4 py-4 pb-20">
        <!-- 订单卡片 1 - 待付款 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-shopping-cart text-blue-500 mr-2"></i>
                        <span class="text-sm text-gray-600">订单号：FJ202403120001</span>
                    </div>
                    <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">待付款</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg mr-3">
                        <img src="https://via.placeholder.com/64x64/C41E3A/FFFFFF?text=汾酒" 
                             alt="汾酒青花瓷" class="w-full h-full object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm mb-1">汾酒青花瓷系列 500ml</h4>
                        <div class="text-xs text-gray-500 mb-1">数量：2件</div>
                        <div class="text-fenjiu-red font-bold">¥2,560</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span><i class="fas fa-user mr-1"></i>买家：张***</span>
                    <span><i class="fas fa-clock mr-1"></i>下单时间：03-12 14:30</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-xs text-gray-500">
                        <i class="fas fa-exclamation-triangle text-orange-500 mr-1"></i>
                        剩余付款时间：23小时45分钟
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-600 text-sm">
                            <i class="fas fa-eye mr-1"></i>详情
                        </button>
                        <button class="text-gray-600 text-sm">
                            <i class="fas fa-phone mr-1"></i>联系
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单卡片 2 - 已付款 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-shopping-cart text-blue-500 mr-2"></i>
                        <span class="text-sm text-gray-600">订单号：FJ202403110002</span>
                    </div>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">已付款</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg mr-3">
                        <img src="https://via.placeholder.com/64x64/D4AF37/FFFFFF?text=汾酒" 
                             alt="汾酒珍藏版" class="w-full h-full object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm mb-1">汾酒珍藏版 1988年</h4>
                        <div class="text-xs text-gray-500 mb-1">数量：1件</div>
                        <div class="text-fenjiu-red font-bold">¥5,800</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span><i class="fas fa-user mr-1"></i>买家：李***</span>
                    <span><i class="fas fa-check mr-1"></i>付款时间：03-11 16:20</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-xs text-green-600">
                        <i class="fas fa-check-circle mr-1"></i>
                        买家已付款，等待发货
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-600 text-sm">
                            <i class="fas fa-eye mr-1"></i>详情
                        </button>
                        <button class="text-fenjiu-red text-sm">
                            <i class="fas fa-shipping-fast mr-1"></i>发货
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单卡片 3 - 已完成 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-shopping-cart text-blue-500 mr-2"></i>
                        <span class="text-sm text-gray-600">订单号：FJ202403100003</span>
                    </div>
                    <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">已完成</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg mr-3">
                        <img src="https://via.placeholder.com/64x64/2C3E50/FFFFFF?text=汾酒" 
                             alt="汾酒陈年老酒" class="w-full h-full object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm mb-1">汾酒陈年老酒 20年</h4>
                        <div class="text-xs text-gray-500 mb-1">数量：1件</div>
                        <div class="text-fenjiu-red font-bold">¥3,200</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span><i class="fas fa-user mr-1"></i>买家：王***</span>
                    <span><i class="fas fa-check mr-1"></i>完成时间：03-10 10:15</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-xs text-gray-600">
                        <i class="fas fa-star text-fenjiu-gold mr-1"></i>
                        买家已确认收货并评价
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-600 text-sm">
                            <i class="fas fa-eye mr-1"></i>详情
                        </button>
                        <button class="text-green-600 text-sm">
                            <i class="fas fa-comment mr-1"></i>评价
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 拍卖订单列表 (隐藏) -->
    <div id="auction-orders" class="px-4 py-4 pb-20 hidden">
        <!-- 拍卖订单卡片 1 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-gavel text-fenjiu-red mr-2"></i>
                        <span class="text-sm text-gray-600">拍卖订单：PM202403120001</span>
                    </div>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">已付款</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg mr-3">
                        <img src="https://via.placeholder.com/64x64/C41E3A/FFFFFF?text=汾酒" 
                             alt="汾酒珍藏版" class="w-full h-full object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm mb-1">汾酒珍藏版 1988年 500ml</h4>
                        <div class="text-xs text-gray-500 mb-1">中标价：¥8,800</div>
                        <div class="text-fenjiu-red font-bold">实付：¥8,800</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span><i class="fas fa-trophy mr-1"></i>中标者：赵***</span>
                    <span><i class="fas fa-check mr-1"></i>付款时间：03-12 20:30</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-xs text-green-600">
                        <i class="fas fa-check-circle mr-1"></i>
                        买家已付款，等待发货
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-600 text-sm">
                            <i class="fas fa-eye mr-1"></i>详情
                        </button>
                        <button class="text-fenjiu-red text-sm">
                            <i class="fas fa-shipping-fast mr-1"></i>发货
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 拍卖订单卡片 2 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-gavel text-fenjiu-red mr-2"></i>
                        <span class="text-sm text-gray-600">拍卖订单：PM202403100002</span>
                    </div>
                    <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">已完成</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg mr-3">
                        <img src="https://via.placeholder.com/64x64/D4AF37/FFFFFF?text=汾酒" 
                             alt="汾酒青花瓷" class="w-full h-full object-cover rounded-lg">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900 text-sm mb-1">汾酒青花瓷系列 限量版</h4>
                        <div class="text-xs text-gray-500 mb-1">中标价：¥2,680</div>
                        <div class="text-fenjiu-red font-bold">实付：¥2,680</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <span><i class="fas fa-trophy mr-1"></i>中标者：孙***</span>
                    <span><i class="fas fa-check mr-1"></i>完成时间：03-10 15:30</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-xs text-gray-600">
                        <i class="fas fa-star text-fenjiu-gold mr-1"></i>
                        交易已完成，获得好评
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-600 text-sm">
                            <i class="fas fa-eye mr-1"></i>详情
                        </button>
                        <button class="text-green-600 text-sm">
                            <i class="fas fa-comment mr-1"></i>评价
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="grid grid-cols-4 py-2">
            <button onclick="navigateTo('dashboard.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">首页</span>
            </button>
            <button onclick="navigateTo('products.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-box text-xl mb-1"></i>
                <span class="text-xs">藏品</span>
            </button>
            <button class="flex flex-col items-center py-2 text-fenjiu-red">
                <i class="fas fa-clipboard-list text-xl mb-1"></i>
                <span class="text-xs">订单</span>
            </button>
            <button onclick="navigateTo('analytics.html')" class="flex flex-col items-center py-2 text-gray-500">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </div>

    <script>
        function navigateTo(page) {
            window.location.href = page;
        }

        function switchTab(type) {
            const salesTab = document.getElementById('sales-tab');
            const auctionTab = document.getElementById('auction-tab');
            const salesOrders = document.getElementById('sales-orders');
            const auctionOrders = document.getElementById('auction-orders');

            if (type === 'sales') {
                salesTab.className = 'px-4 py-2 bg-fenjiu-red text-white rounded-full text-sm';
                auctionTab.className = 'px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm';
                salesOrders.classList.remove('hidden');
                auctionOrders.classList.add('hidden');
            } else {
                salesTab.className = 'px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm';
                auctionTab.className = 'px-4 py-2 bg-fenjiu-red text-white rounded-full text-sm';
                salesOrders.classList.add('hidden');
                auctionOrders.classList.remove('hidden');
            }
        }
    </script>
</body>
</html>
